//
//  CDMessageAction+CoreDataProperties.swift
//  lavachat
//
//  Created by ht on 2025/7/4.
//
//

import Foundation
import CoreData


extension CDMessageAction {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<CDMessageAction> {
        return NSFetchRequest<CDMessageAction>(entityName: "CDMessageAction")
    }

    @NSManaged public var id: UUID
    @NSManaged public var name: String
    @NSManaged public var icon: String?
    @NSManaged public var actionType: Data // Stores encoded ActionType enum
    @NSManaged public var prompts: Data? // JSON encoded [String]
    @NSManaged public var targetLLMInstanceId: UUID?
    @NSManaged public var metadata: Data? // JSON encoded [String: String]

}

extension CDMessageAction : Identifiable {

}
