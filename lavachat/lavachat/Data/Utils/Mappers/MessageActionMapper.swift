import Foundation
import CoreData

/// Mapper for converting between MessageAction domain entity and CDMessageAction Core Data entity
struct MessageActionMapper {

    /// Converts a CDMessageAction entity to a domain MessageAction entity
    static func toDomain(cdAction: CDMessageAction) -> MessageAction {
        let prompts: [String] = EntityMapper.decode(cdAction.prompts) ?? []
        let actionType: MessageAction.ActionType = EntityMapper.decode(cdAction.actionType) ?? .assistantRegenerate
        let metadata: [String: String] = EntityMapper.decode(cdAction.metadata) ?? [:]

        return MessageAction(
            id: cdAction.id,
            name: cdAction.name,
            icon: cdAction.icon,
            actionType: actionType,
            prompts: prompts,
            targetLLMInstanceId: cdAction.targetLLMInstanceId,
            metadata: metadata
        )
    }
    
    /// Converts a domain MessageAction entity to a new CDMessageAction entity
    static func toCoreData(action: MessageAction, context: NSManagedObjectContext) -> CDMessageAction {
        let cdAction = CDMessageAction(context: context)
        cdAction.id = action.id
        updateCoreData(cdAction: cdAction, with: action)
        return cdAction
    }
    
    /// Updates an existing CDMessageAction entity with values from a domain MessageAction entity
    static func updateCoreData(cdAction: CDMessageAction, with action: MessageAction) {
        // ID is set on creation and should not be changed.
        cdAction.name = action.name
        cdAction.icon = action.icon
        cdAction.actionType = EntityMapper.encode(action.actionType) ?? Data()
        cdAction.prompts = EntityMapper.encode(action.prompts)
        cdAction.targetLLMInstanceId = action.targetLLMInstanceId
        cdAction.metadata = EntityMapper.encode(action.metadata)
    }
}
