import XCTest
import os
@testable import lavachat

final class ImportServiceImplTests: XCTestCase {
    
    // MARK: - Test Properties
    
    var importService: ImportServiceImpl!
    var chatRepository: ChatRepository!
    var llmRepository: LLMInstanceRepository!
    var testUserId: UUID!
    private let logger = Logger(subsystem: "lavachat.tests", category: "ImportServiceImplTests")
    
    // Test data
    var testProvider: LLMProvider!
    var testModel: LLMModel!
    var testInstance: LLMInstance!
    var testAction: MessageAction!
    var testSetting: ChatSessionSetting!
    var testSession: ChatSession!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        let container = DIContainer(context: PersistenceController.shared.container.viewContext)
        chatRepository = container.getSharedChatRepository()
        llmRepository = container.getSharedLLMInstanceRepository()
        importService = ImportServiceImpl(
            fileShareHandler: try! FileShareHandler(),
            cloudKitShareHandler: CloudKitShareHandler(),
            qrCodeShareHandler: QRCodeShareHandler(),
            llmRepository: llmRepository,
            chatRepository: chatRepository
        )
        testUserId = UUID()
        
        setupTestData()
    }
    
    override func tearDown() {
        super.tearDown()
        importService = nil
        chatRepository = nil
        llmRepository = nil
        testUserId = nil
    }
    
    private func setupTestData() {
        testProvider = LLMProvider(
            name: "Test Provider",
            apiBaseUrl: "https://api.test.com",
            providerType: .userApiKey,
            apiKeyStored: false, // Cleaned for sharing
            apiStyle: .openaiCompatible,
            apiEndpointPath: "/v1/chat/completions",
            isUserCreated: true,
            isUserModified: false
        )

        testModel = LLMModel(
            providerId: testProvider.id,
            modelIdentifier: "test-model-v1",
            name: "Test Model",
            contextWindowSize: 4096,
            maxOutputTokens: 2048,
            isUserCreated: true,
            isUserModified: false
        )

        testInstance = LLMInstance(
            modelId: testModel.id,
            name: "Test Instance",
            systemPrompt: "You are a helpful assistant",
            isUserModified: false
        )

        testAction = MessageAction(
            name: "Test Action",
            icon: "star.fill",
            actionType: .assistantRegenerate,
            prompts: ["Test prompt"],
            targetLLMInstanceId: testInstance.id
        )

        let messageActionSettings = MessageActionSettings(
            actionPanelActions: [],
            userMessageActions: [],
            assistantMessageCardActions: [testAction.id],
            assistantMessageMenuActions: []
        )

        testSetting = ChatSessionSetting(
            name: "Test Setting",
            isSystemDefault: false,
            shouldExpandThinking: true,
            messageActionSettings: messageActionSettings,
            auxiliaryLLMInstanceId: testInstance.id,
            shouldAutoGenerateTitle: true,
            contextMessageCount: 10
        )

        testSession = ChatSession(
            title: "Test Chat Session",
            activeLLMInstanceIds: [testInstance.id],
            usedLLMInstanceIds: [testInstance.id],
            settingsId: testSetting.id,
            userId: testUserId
        )
    }
    
    // MARK: - Import LLMInstance Tests
    
    func testImportLLMInstanceWithDependencies() async throws {
        // Given
        let shareableData = ShareableData(
            shareType: .llmInstance,
            data: ShareableDataContent(
                instance: testInstance,
                model: testModel,
                provider: testProvider
            ),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )

        // When - Create a temporary file for testing
        let tempURL = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("test.lavachat")
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let jsonData = try encoder.encode(shareableData)
        try jsonData.write(to: tempURL)

        let result = await importService.importFromFile(
            tempURL,
            configuration: ImportConfiguration()
        )

        // Then
        switch result {
        case .success(let successInfo):
            XCTAssertEqual(successInfo.importedItemIds.count, 3) // Provider, Model, Instance
            XCTAssertTrue(successInfo.conflictsResolved.isEmpty)
            XCTAssertTrue(successInfo.skippedItemIds.isEmpty) // No items should be skipped for new import
        case .failure(let error):
            XCTFail("Import should succeed, but failed with: \(error)")
        }

        // Verify the items were actually created in the repository
        let createdProvider = try await llmRepository.getProvider(byId: testProvider.id)
        let createdModel = try await llmRepository.getModel(byId: testModel.id)
        let createdInstance = try await llmRepository.getInstance(byId: testInstance.id)

        XCTAssertNotNil(createdProvider)
        XCTAssertNotNil(createdModel)
        XCTAssertNotNil(createdInstance)
    }
    
    // MARK: - Import ChatSessionSetting Tests
    
    func testImportChatSessionSettingWithMessageActions() async throws {
        // Given
        let shareableData = ShareableData(
            shareType: .chatSessionSetting,
            data: ShareableDataContent(
                messageActions: [testAction],
                chatSessionSetting: testSetting
            ),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )

        // When - Create a temporary file for testing
        let tempURL = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("test2.lavachat")
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let jsonData = try encoder.encode(shareableData)
        try jsonData.write(to: tempURL)

        let result = await importService.importFromFile(
            tempURL,
            configuration: ImportConfiguration()
        )

        // Then
        switch result {
        case .success(let successInfo):
            XCTAssertEqual(successInfo.importedItemIds.count, 2) // MessageAction + ChatSessionSetting
            XCTAssertTrue(successInfo.conflictsResolved.isEmpty)
            XCTAssertTrue(successInfo.skippedItemIds.isEmpty) // No items should be skipped for new import
        case .failure(let error):
            XCTFail("Import should succeed, but failed with: \(error)")
        }

        // Verify the items were actually created in the repository
        let createdAction = try await chatRepository.getMessageAction(byId: testAction.id)
        let createdSetting = try await chatRepository.getSetting(byId: testSetting.id)

        XCTAssertNotNil(createdAction)
        XCTAssertNotNil(createdSetting)
    }
    
    // MARK: - Import ChatSession Tests
    
    func testImportChatSessionWithDependencies() async throws {
        // Given
        let shareableData = ShareableData(
            shareType: .chatSession,
            data: ShareableDataContent(
                chatSession: testSession,
                messageActions: [testAction],
                chatSessionSetting: testSetting
            ),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )

        // When - Create a temporary file for testing
        let tempURL = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("test3.lavachat")
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let jsonData = try encoder.encode(shareableData)
        try jsonData.write(to: tempURL)

        let result = await importService.importFromFile(
            tempURL,
            configuration: ImportConfiguration()
        )

        // Then
        switch result {
        case .success(let successInfo):
            XCTAssertEqual(successInfo.importedItemIds.count, 3) // MessageAction + Setting + Session
            XCTAssertTrue(successInfo.conflictsResolved.isEmpty)
            XCTAssertTrue(successInfo.skippedItemIds.isEmpty) // No items should be skipped for new import
        case .failure(let error):
            XCTFail("Import should succeed, but failed with: \(error)")
        }

        // Verify the items were actually created in the repository
        let createdAction = try await chatRepository.getMessageAction(byId: testAction.id)
        let createdSetting = try await chatRepository.getSetting(byId: testSetting.id)
        let createdSession = try await chatRepository.getChatSession(byId: testSession.id)

        XCTAssertNotNil(createdAction)
        XCTAssertNotNil(createdSetting)
        XCTAssertNotNil(createdSession)
    }
    
    // MARK: - Conflict Resolution Tests
    
    func testImportWithConflictResolution() async throws {
        // Given - First create the existing action in the repository
        try await chatRepository.createMessageAction(testAction)

        // Create a different action with the same ID to test conflict resolution
        let conflictingAction = MessageAction(
            id: testAction.id, // Same ID
            name: "Different Name", // Different content
            icon: "different.icon",
            actionType: .actionPanelPromptInsert,
            prompts: ["Different prompt"],
            targetLLMInstanceId: nil,
            metadata: ["key": "different value"]
        )

        let shareableData = ShareableData(
            shareType: .messageAction,
            data: ShareableDataContent(messageAction: conflictingAction),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )

        // When - Create a temporary file for testing
        let tempURL = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("test4.lavachat")
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let jsonData = try encoder.encode(shareableData)
        try jsonData.write(to: tempURL)

        let result = await importService.importFromFile(
            tempURL,
            configuration: ImportConfiguration()
        )

        // Then
        switch result {
        case .success(let successInfo):
            logger.info("Import succeeded. ImportedIds: \(successInfo.importedItemIds.count), Conflicts: \(successInfo.conflictsResolved), Skipped: \(successInfo.skippedItemIds.count)")
            XCTAssertEqual(successInfo.importedItemIds.count, 1)
            XCTAssertFalse(successInfo.conflictsResolved.isEmpty)
            XCTAssertTrue(successInfo.conflictsResolved.first?.contains("renamed due to conflict") == true)
            XCTAssertTrue(successInfo.skippedItemIds.isEmpty) // No items should be skipped in conflict case
        case .failure(let error):
            logger.error("Import failed with error: \(error)")
            XCTFail("Import should succeed, but failed with: \(error)")
        }

        // Verify we have two actions now (original + imported with modified name)
        let allActions = try await chatRepository.getAllMessageActions()
        let importedActions = allActions.filter { $0.name.contains("(Imported)") }
        logger.info("Total actions: \(allActions.count), Imported actions: \(importedActions.count)")
        for action in allActions {
            logger.info("Action: \(action.name) (ID: \(action.id))")
        }
        XCTAssertEqual(importedActions.count, 1)
        XCTAssertNotEqual(importedActions.first?.id, testAction.id)
    }

    // MARK: - Progress Tests
    
    func testImportProgress() async throws {
        // Given
        let shareableData = ShareableData(
            shareType: .messageAction,
            data: ShareableDataContent(messageAction: testAction),
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )
        
        // When - Create a temporary file for testing
        let tempURL = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("test5.lavachat")
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let jsonData = try encoder.encode(shareableData)
        try jsonData.write(to: tempURL)

        let result = await importService.importFromFile(
            tempURL,
            configuration: ImportConfiguration()
        )

        // Then
        switch result {
        case .success(let successInfo):
            XCTAssertNotNil(successInfo)
        case .failure(let error):
            XCTFail("Import should succeed, but failed with: \(error)")
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testImportWithMissingRequiredData() async {
        // Given
        let shareableData = ShareableData(
            shareType: .llmInstance,
            data: ShareableDataContent(), // Missing instance data
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )
        
        // When & Then - Create a temporary file for testing
        let tempURL = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("test6.lavachat")
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        do {
            let jsonData = try encoder.encode(shareableData)
            try jsonData.write(to: tempURL)
        } catch {
            XCTFail("Failed to create test file: \(error)")
            return
        }

        let result = await importService.importFromFile(
            tempURL,
            configuration: ImportConfiguration()
        )

        switch result {
        case .success(_):
            XCTFail("Should fail for missing required data")
        case .failure(let error):
            XCTAssertTrue(error is ImportError)
        }
    }

    func testImportIdenticalItemsSkipped() async throws {
        // Given - Get initial count and create the action in the repository
        let initialActions = try await chatRepository.getAllMessageActions()
        let initialCount = initialActions.count

        try await chatRepository.createMessageAction(testAction)

        // Create identical action data for import
        let shareableData = ShareableData(
            shareType: .messageAction,
            data: ShareableDataContent(messageAction: testAction), // Identical action
            metadata: ShareableMetadata(
                appVersion: "1.0",
                deviceInfo: "Test Device",
                exportedBy: "Test User"
            )
        )

        // When - Create a temporary file for testing
        let tempURL = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("test_identical.lavachat")
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let jsonData = try encoder.encode(shareableData)
        try jsonData.write(to: tempURL)

        let result = await importService.importFromFile(
            tempURL,
            configuration: ImportConfiguration()
        )

        // Then
        switch result {
        case .success(let successInfo):
            XCTAssertEqual(successInfo.importedItemIds.count, 0) // No new items imported
            XCTAssertTrue(successInfo.conflictsResolved.isEmpty) // No conflicts
            XCTAssertEqual(successInfo.skippedItemIds.count, 1) // One item skipped
            XCTAssertEqual(successInfo.skippedItemIds.first, testAction.id) // Correct item skipped
        case .failure(let error):
            XCTFail("Import should succeed, but failed with: \(error)")
        }

        // Verify count didn't change (no new action was added)
        let finalActions = try await chatRepository.getAllMessageActions()
        XCTAssertEqual(finalActions.count, initialCount + 1) // Only the one we created initially

        // Verify our test action still exists
        let ourAction = finalActions.first { $0.id == testAction.id }
        XCTAssertNotNil(ourAction)
        XCTAssertEqual(ourAction?.name, testAction.name)
    }
}
